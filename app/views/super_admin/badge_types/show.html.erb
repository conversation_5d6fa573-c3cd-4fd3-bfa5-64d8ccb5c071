<% content_for :title, @badge_type.name %>

<div class="container mx-auto px-4">
  <div class="flex items-center justify-between mb-4">
    <h1 class="text-2xl font-bold">Badge: <%= @badge_type.name %></h1>
    <div>
      <%= link_to "Edit", edit_super_admin_badge_type_path(@badge_type), class: "btn-secondary" %>
      <%= link_to "Back to Badge Types", super_admin_badge_types_path, class: "btn-secondary" %>
    </div>
  </div>

  <div class="bg-white shadow-md rounded-lg p-6 mb-6">
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
      <div>
        <h3 class="text-lg font-medium text-gray-900">Preview</h3>
        <div class="mt-2">
          <span class="px-4 py-2 rounded-lg text-lg font-semibold" style="background-color: <%= @badge_type.background_color %>; color: <%= @badge_type.text_color %>;">
            <i class="<%= @badge_type.icon %> mr-2"></i>
            <%= @badge_type.name %>
          </span>
        </div>
      </div>
      <div>
        <h3 class="text-lg font-medium text-gray-900">Details</h3>
        <dl class="mt-2 space-y-2">
          <dt class="text-sm font-medium text-gray-500">Description</dt>
          <dd class="text-sm text-gray-900"><%= @badge_type.description %></dd>
          <dt class="text-sm font-medium text-gray-500">Priority</dt>
          <dd class="text-sm text-gray-900"><%= @badge_type.priority %></dd>
          <dt class="text-sm font-medium text-gray-500">Status</dt>
          <dd class="text-sm text-gray-900">
            <% if @badge_type.active? %>
              <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                Active
              </span>
            <% else %>
              <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                Inactive
              </span>
            <% end %>
          </dd>
        </dl>
      </div>
      <div>
        <h3 class="text-lg font-medium text-gray-900">Colors & Icon</h3>
        <dl class="mt-2 space-y-2">
          <dt class="text-sm font-medium text-gray-500">Background</dt>
          <dd class="text-sm text-gray-900 flex items-center">
            <span class="w-4 h-4 rounded-full mr-2" style="background-color: <%= @badge_type.background_color %>;"></span>
            <%= @badge_type.background_color %>
          </dd>
          <dt class="text-sm font-medium text-gray-500">Text</dt>
          <dd class="text-sm text-gray-900 flex items-center">
            <span class="w-4 h-4 rounded-full mr-2" style="background-color: <%= @badge_type.text_color %>;"></span>
            <%= @badge_type.text_color %>
          </dd>
          <dt class="text-sm font-medium text-gray-500">Icon</dt>
          <dd class="text-sm text-gray-900">
            <i class="<%= @badge_type.icon %> mr-1"></i>
            <%= @badge_type.icon %>
          </dd>
        </dl>
      </div>
    </div>
  </div>

  <h2 class="text-xl font-bold mb-4">Assignments (<%= @assignments.total_count %>)</h2>

  <div class="bg-white shadow-md rounded-lg overflow-hidden">
    <table class="min-w-full divide-y divide-gray-200">
      <thead class="bg-gray-50">
        <tr>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Assigned By</th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Assigned At</th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Expires At</th>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-gray-200">
        <% @assignments.each do |assignment| %>
          <tr>
            <td class="px-6 py-4 whitespace-nowrap">
              <%= link_to assignment.user.name, super_admin_user_path(assignment.user), class: "text-blue-600 hover:underline" %>
            </td>
            <td class="px-6 py-4 whitespace-nowrap"><%= assignment.admin.name %></td>
            <td class="px-6 py-4 whitespace-nowrap"><%= l(assignment.assigned_at, format: :long) %></td>
            <td class="px-6 py-4 whitespace-nowrap"><%= assignment.expires_at ? l(assignment.expires_at, format: :long) : "Never" %></td>
          </tr>
        <% end %>
      </tbody>
    </table>
    <div class="p-4">
      <%= paginate @assignments %>
    </div>
  </div>
</div>